\chapter{General Project Context}

\section{Introduction}
This chapter introduces the MED4SOLUTIONS internship project, conducted within a digital health startup focused on transforming medication management and delivery. The goal is to explore the challenges in the current pharmaceutical ecosystem and to describe how our solution leverages modern technologies to deliver a secure, efficient, and patient-centric experience.

\section{Introduction to the Host Organization}

\subsection{Presentation of the Company: MED4SOLUTIONS}
MED4SOLUTIONS is a Tunisian digital health startup committed to enhancing patient care through smart technology. It focuses on delivering innovative solutions that connect pharmacies, patients, and delivery networks in a secure and seamless ecosystem. The company's mission is to modernize healthcare logistics by improving how prescriptions are handled, medications are delivered, and communication is facilitated among stakeholders.

\subsection{MED4SOLUTIONS Products}
MED4SOLUTIONS offers a web and mobile platform that allows patients to:
\begin{itemize}
    \item Upload and manage their prescriptions
    \item Order medications securely online
    \item Track deliveries in real time
    \item Receive personalized reminders for medication intake
\end{itemize}

The platform integrates AI-based prescription scanning, secure payment gateways, real-time chat with pharmacies, and stock management tools for pharmacists.

\subsection{Partners and References: MED4SOLUTIONS's Ecosystem}
MED4SOLUTIONS collaborates with a growing network of pharmacies, delivery service providers, and healthcare professionals. Its ecosystem includes:
\begin{itemize}
    \item Partner pharmacies for fulfillment and verification
    \item Local delivery networks for logistics
    \item Healthcare IT providers for technical integration
\end{itemize}
This ecosystem ensures that the platform remains scalable, compliant, and responsive to patient needs.

\section{Project Overview}

\subsection{Target Users}
The MED4SOLUTIONS platform primarily serves:
\begin{itemize}
    \item Patients who require remote medication management and delivery
    \item Pharmacies looking to digitize prescription handling and optimize inventory
    \item Delivery personnel involved in healthcare logistics
    \item Administrators who oversee system performance and ensure compliance
\end{itemize}

\subsection{Usage Example}
A typical user uploads a prescription through the mobile app, which is then validated by a pharmacy using AI tools. The patient selects home delivery, and the delivery personnel updates the order status in real-time. The system sends reminders for medication intake and allows follow-up chat with the pharmacist.

\section{Study of the Existing System}

This section explores four leading digital pharmacy platforms to analyze how they manage medication delivery and patient services. We evaluate their features and limitations to better identify the gaps that MED4SOLUTIONS aims to address.

\subsection{Capsule Pharmacy}
Capsule is a modern, app-based pharmacy that offers free, same-day delivery of prescription medications directly to customers' homes or workplaces. Through its user-friendly mobile application, users can manage prescriptions, consult with pharmacists via text or phone, and schedule deliveries at their convenience. Capsule accepts prescriptions from any doctor and ensures that customers only pay their copay, with no additional fees for the service.

\subsubsection{Key Features}
\begin{itemize}
    \item Free same-day delivery of medications
    \item In-app communication with pharmacists for consultations
    \item Easy prescription management and refills
    \item Transparent pricing with no hidden fees
\end{itemize}

\begin{figure}[H]
\centering
\includegraphics[width=0.6\textwidth]{img/capsulePharmacyLogo.png}
\caption{Capsule Pharmacy Interface}
\label{fig:capsule}
\end{figure}

\subsection{PocketPills}
PocketPills is a Canadian online pharmacy that offers free medication delivery and 24/7 pharmacist support. It provides a convenient solution for patients who want to manage prescriptions from home, focusing on accessibility and ease of use for Canadian patients.

\subsubsection{Key Features}
\begin{itemize}
    \item Free medication delivery across Canada
    \item 24/7 access to licensed pharmacists
    \item Online prescription management
    \item Automatic prescription refill reminders
\end{itemize}

\begin{figure}[htpb]
\centering
\includegraphics[width=0.3\textwidth]{img/PocketPills.png}
\caption{PocketPills Logo}
\label{fig:pocketpills}
\end{figure}

\subsection{Express Scripts}
Express Scripts is a U.S.-based pharmacy benefit management service that enables virtual medication ordering and prescription handling. It serves as a comprehensive platform for managing prescription benefits and medication delivery for large organizations and health plans.

\subsubsection{Key Features}
\begin{itemize}
    \item Virtual medication ordering and management
    \item 24/7 pharmacist support and consultation
    \item Integration with health insurance plans
    \item Prescription benefit management for organizations
\end{itemize}

\begin{figure}[htpb]
\centering
\includegraphics[width=0.3\textwidth]{img/ExpressScripts.png}
\caption{Express Scripts Logo}
\label{fig:expressscripts}
\end{figure}

\subsection{Mednow}
Mednow is a full-service digital pharmacy offering fast delivery and easy prescription ordering. The platform focuses on providing accessible healthcare solutions with emphasis on speed and convenience for patients across various locations.

\subsubsection{Key Features}
\begin{itemize}
    \item Fast medication delivery service
    \item Easy online prescription ordering
    \item Full-service digital pharmacy operations
    \item Simple prescription management interface
\end{itemize}

\begin{figure}[htpb]
\centering
\includegraphics[width=0.3\textwidth]{img/MedNow.png}
\caption{Mednow Logo}
\label{fig:mednow}
\end{figure}

\section{Critique of the Existing System}

\subsection{Introduction}
The analyzed digital pharmacy platforms (Capsule Pharmacy, PocketPills, Express Scripts, and Mednow) offer various approaches to prescription management and medication delivery. While each platform addresses specific aspects of medication accessibility and user convenience, they all share common limitations that could be improved upon through innovative solutions.

\subsection{Advantages}
\begin{itemize}
    \item \textbf{Convenience and Accessibility:} All platforms provide delivery services, with Capsule and PocketPills offering free same-day delivery, allowing users to receive medications without visiting physical pharmacies.
    \item \textbf{User-Friendly Interfaces:} Capsule, PocketPills, and Mednow feature intuitive designs that make prescription management and ordering straightforward for users.
    \item \textbf{Professional Support:} Capsule, PocketPills, and Express Scripts provide 24/7 access to licensed pharmacists through various communication channels, ensuring professional guidance.
    \item \textbf{Transparent Pricing:} Capsule eliminates hidden fees, while Express Scripts integrates with insurance plans to provide clear benefit management.
    \item \textbf{Integration Capabilities:} Capsule allows direct prescription submission from healthcare providers, and Express Scripts integrates with organizational health plans.
    \item \textbf{Automated Features:} PocketPills offers automatic prescription refill reminders, enhancing medication adherence.
\end{itemize}

\subsection{Disadvantages}
\begin{itemize}
    \item \textbf{Limited Geographic Coverage:} Most platforms have restricted service areas, with Capsule limited to specific U.S. cities, PocketPills to Canada, and varying coverage for other platforms.
    \item \textbf{No Real-Time Order Tracking:} None of the analyzed platforms provide comprehensive real-time tracking for medication deliveries, reducing transparency and user satisfaction.
    \item \textbf{Lack of AI Integration:} All platforms lack AI-powered features such as prescription scanning, error detection, automated validation, or personalized health recommendations.
    \item \textbf{Limited Communication Features:} While some offer pharmacist consultation, none provide comprehensive in-app chat systems connecting patients, pharmacies, and delivery personnel.
    \item \textbf{Insufficient Patient Engagement Tools:} Most platforms lack comprehensive medication reminders, health monitoring features, or integration with wearable devices for chronic care management.
    \item \textbf{No Delivery Confirmation Systems:} None of the platforms allow users to confirm delivery receipt in-app, which could improve delivery accuracy and accountability.
    \item \textbf{Limited Personalization:} The platforms do not offer personalized health insights, medication interaction warnings, or tailored treatment recommendations.
\end{itemize}

\subsection{Conclusion}
By analyzing these advantages and disadvantages across multiple digital pharmacy platforms, we can identify significant gaps in the current market. These findings highlight the need for a comprehensive solution that combines the strengths of existing platforms while addressing their common limitations through innovative features like AI integration, real-time tracking, enhanced communication tools, and personalized patient engagement systems.

\section{Problem Statement}

\subsection{Identified Challenges}
In the context of developing MED4SOLUTIONS, a digital health startup focused on medication management and delivery, we identified several critical challenges that existing pharmaceutical delivery systems fail to address adequately. These challenges affect patients, pharmacies, and delivery personnel across the healthcare ecosystem:

\begin{itemize}
    \item \textbf{Inefficient Prescription Management:} Current systems lack automation for prescription validation, often requiring manual processing by pharmacies, which increases the risk of errors and delays.
    \item \textbf{Limited Access to Medication Delivery Services:} Many patients, particularly those in underserved areas, do not have access to reliable, same-day medication delivery options, creating barriers to timely treatment.
    \item \textbf{Fragmented Communication:} Communication between patients, pharmacies, and delivery personnel is often fragmented or nonexistent, leading to miscommunication and inefficiencies in managing orders and resolving issues.
    \item \textbf{Absence of Real-Time Tracking:} Patients frequently lack visibility into the status of their orders and deliveries, creating uncertainty and frustration about when medications will arrive.
    \item \textbf{Inadequate Patient Engagement Tools:} Existing systems do not provide comprehensive features such as medication reminders, health monitoring, or personalized alerts, which are critical for improving medication adherence and health outcomes.
    \item \textbf{Lack of Integration:} Current platforms often fail to integrate seamlessly with pharmacy inventory systems, payment systems, or healthcare providers, limiting their scalability and efficiency.
\end{itemize}

\subsection{Conclusion}
These challenges highlight the need for a comprehensive solution that streamlines prescription management, improves accessibility to medication delivery, enhances communication among stakeholders, and provides personalized tools for patients to manage their health effectively.

\section{Proposed Solution}

\subsection{Introduction}
MED4SOLUTIONS addresses the identified challenges through the development of a comprehensive digital health platform that integrates mobile and web applications for medication management and delivery. Our solution creates an ecosystem that seamlessly connects patients, pharmacies, and delivery personnel, leveraging modern technologies including artificial intelligence, real-time tracking, and secure communication channels to revolutionize the pharmaceutical delivery experience.

\subsection{Key Features of the Proposed System}

\subsubsection{Advanced Prescription Management}
\begin{itemize}
    \item Allow patients to upload prescriptions as photos or PDFs directly through the app
    \item Integrate AI-powered scanning and validation to digitize prescriptions, detect errors, and improve efficiency
    \item Enable pharmacies to validate, update, and store prescriptions securely
\end{itemize}

\subsubsection{Medication Ordering and Delivery}
\begin{itemize}
    \item Provide patients with the ability to browse available medications, place orders, and select delivery or pickup options
    \item Include real-time order tracking for patients, ensuring visibility throughout the delivery process
    \item Allow delivery personnel to update order statuses (e.g., "Picked Up," "In Transit," "Delivered") in real time
\end{itemize}

\subsubsection{Communication Features}
\begin{itemize}
    \item Implement in-app real-time chat to facilitate communication between patients and pharmacies for inquiries about medications
    \item Include messaging options for patients to contact delivery personnel regarding order or location details
\end{itemize}

\subsubsection{Patient Engagement Tools}
\begin{itemize}
    \item Provide personalized medication reminders to ensure patients take their medications on time
    \item Include alerts for prescription renewals and health monitoring features to enhance adherence and outcomes
\end{itemize}

\subsubsection{Pharmacy and Stock Management}
\begin{itemize}
    \item Enable pharmacies to manage their stock, update medication availability, and track orders efficiently
    \item Allow system administrators to monitor and optimize pharmacy performance and inventory
\end{itemize}

\subsubsection{Secure Payment Integration}
\begin{itemize}
    \item Offer secure online payment options through trusted gateways, ensuring smooth transactions for patients
    \item Support features for payment tracking, refunds, and payment confirmations
\end{itemize}

\subsubsection{Scalability and Accessibility}
\begin{itemize}
    \item The application will be accessible on both mobile (iOS and Android) and web platforms, ensuring flexibility and convenience for users
    \item Design the system to handle a growing user base and support multiple pharmacies and delivery networks
\end{itemize}

\subsection{Conclusion}
MED4SOLUTIONS represents a comprehensive digital health platform that transforms medication management and delivery through innovative technology integration. The platform empowers patients with intuitive prescription management, real-time order tracking, and personalized health tools, while providing pharmacies with advanced validation systems, inventory management, and secure payment processing. Delivery personnel benefit from efficient status update mechanisms and optimized routing, creating a cohesive ecosystem that enhances healthcare accessibility and patient outcomes.

\section{Added Value}

MED4SOLUTIONS delivers transformative value across the healthcare ecosystem by addressing critical gaps in current medication management and delivery systems. As a digital health startup, our platform creates measurable benefits for all stakeholders:

\begin{itemize}
    \item \textbf{Enhanced Accessibility:} Patients can conveniently manage prescriptions, order medications, and track deliveries from anywhere using the mobile or web platform.
    \item \textbf{Improved Efficiency:} AI-powered prescription validation and streamlined communication channels will reduce manual errors, save time, and optimize pharmacy operations.
    \item \textbf{Personalized Patient Care:} Features like medication reminders, prescription renewal alerts, and health monitoring tools will help patients adhere to their treatment plans, improving health outcomes.
    \item \textbf{Real-Time Visibility:} Real-time order tracking and delivery updates will provide transparency and reduce uncertainty for patients, while pharmacies can monitor order progress more effectively.
    \item \textbf{Scalability and Flexibility:} The system will be designed to support a growing user base, multiple pharmacies, and delivery networks, ensuring long-term usability and adaptability.
    \item \textbf{Secure Transactions:} Integration with trusted payment gateways will ensure secure and seamless financial transactions for patients and pharmacies.
    \item \textbf{Fostering Collaboration:} The platform will connect patients, pharmacies, and delivery personnel, creating a seamless ecosystem that enhances communication and coordination among all stakeholders.
\end{itemize}

\section{Development Methodology}

For the development of the MED4SOLUTIONS platform, we implemented the Scrum framework under the supervision of Scrum Master Ibrahim Dhouib at MED4SOLUTIONS, with technical guidance from supervisors Ziyed Dammak and Ayoub Kessemtini. This agile methodology was chosen for its emphasis on iterative development, team collaboration, and adaptability — essential qualities for developing a complex healthcare technology platform where requirements may evolve based on user feedback and regulatory considerations.

\subsection{Scrum Team Definition}
The following table defines the roles and responsibilities within our Scrum team:

\begin{table}[H]
\centering
\begin{tabular}{|p{3cm}|p{6cm}|p{4cm}|}
\hline
\textbf{Role} & \textbf{Missions} & \textbf{Actor} \\
\hline
Product Owner & Define product vision, manage product backlog, prioritize features, and ensure stakeholder requirements are met & Ibrahim Dhouib \\
\hline
Scrum Master & Facilitate Scrum processes, remove impediments, coach the team, and ensure adherence to Scrum principles & Ziyed Dammak \\
\hline
Development Team & Design, develop, test, and deliver working software increments during each sprint & Hssan Ghorbel \\
\hline
\end{tabular}
\caption{Scrum Team Roles and Responsibilities}
\label{tab:scrum-team}
\end{table}

\subsection{Why Scrum?}
\begin{itemize}
    \item \textbf{Sprint Planning:} Break down the development process into manageable chunks of work (sprints), focusing on addressing specific shortcomings identified in the existing system.
    \item \textbf{Daily Stand-Ups:} Hold regular meetings to discuss progress, identify any obstacles, and adjust plans accordingly.
    \item \textbf{Continuous Feedback:} Regularly gather feedback from stakeholders to ensure the application meets their needs and expectations.
    \item \textbf{Iterative Development:} Release new features incrementally, allowing for early testing and validation with real users.
    \item \textbf{Adaptability:} Be prepared to adapt and adjust the development plan based on feedback and changing requirements throughout the project lifecycle.
\end{itemize}

By employing an Agile methodology, the development team can effectively analyze the existing system, address identified shortcomings, and deliver a comprehensive solution that meets user needs and expectations.

\section{Conclusion}
In this chapter, we introduced the MED4SOLUTIONS project and its hosting company, highlighting the mission and core solutions offered. We explored the target users and a real-world usage example to contextualize the platform's purpose. Finally, by analyzing an existing benchmark like Capsule Pharmacy, we identified several limitations in the current healthcare delivery landscape. These findings form the foundation for proposing a tailored, innovative solution that addresses these gaps in the following chapters.
